# Copyright (c) 2025, NVIDIA CORPORATION.  All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: "cosyvoice2"
backend: "python"
max_batch_size: ${triton_max_batch_size}
dynamic_batching {
    max_queue_delay_microseconds: ${max_queue_delay_microseconds}
}
model_transaction_policy {
  decoupled: ${decoupled_mode}
}
parameters [
  {
   key: "llm_tokenizer_dir", 
   value: {string_value:"${llm_tokenizer_dir}"}
  },
  {
   key: "model_dir", 
   value: {string_value:"${model_dir}"}
  }
]

input [
  {
    name: "reference_wav"
    data_type: TYPE_FP32
    dims: [-1]
  },
  {
    name: "reference_wav_len"
    data_type: TYPE_INT32
    dims: [1]
  },
  {
    name: "reference_text"
    data_type: TYPE_STRING
    dims: [1]
  },
  {
    name: "target_text"
    data_type: TYPE_STRING
    dims: [1]
  }
]
output [
  {
    name: "waveform"
    data_type: TYPE_FP32
    dims: [ -1 ]
  }
]

instance_group [
  {
    count: ${bls_instance_num}
    kind: KIND_CPU
  }
]